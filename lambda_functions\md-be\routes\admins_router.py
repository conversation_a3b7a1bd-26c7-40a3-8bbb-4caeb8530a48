from fastapi import APIRouter, Depends, status, Query
from fastapi.security import HTTPAuthorizationCredentials, HTTP<PERSON>earer
from sqlalchemy.orm import Session
from controller.admins_controller import AdminController
from controller.database_utils_controller import DatabaseUtilsController
from db.db import get_db
from dependencies.admin_jwt import validate_jwt_token
from schemas.admin import Admin_cognito_create, AdminUpdate, ForgotPasswordRequest, ConfirmForgotPasswordRequest, ChangePasswordRequest
from schemas.database_utils import SequenceResetRequest
from jose import jwt
from schemas.member import CoMemberCreate
from controller.member_authentication_controller import MemberAuthenticationController

router = APIRouter()

adminController = AdminController()
member_auth_controller = MemberAuthenticationController()
database_utils_controller = DatabaseUtilsController()

@router.post("/create-member", dependencies=[Depends(validate_jwt_token)], status_code=status.HTTP_201_CREATED)
async def create_member_by_admin(
    member: CoMemberCreate,
    db: Session = Depends(get_db),
    admin_user_payload=Depends(validate_jwt_token)
):
    return member_auth_controller.create_member(member, admin_user_payload, db)

@router.post("/register", dependencies=[Depends(validate_jwt_token)], status_code=status.HTTP_201_CREATED)
async def create_admin_cognito(
    new_admin: Admin_cognito_create,
    db: Session = Depends(get_db),
    credentials: HTTPAuthorizationCredentials = Depends(HTTPBearer())
):
    """Create a new admin with Cognito integration"""
    return adminController.create_admin_cognito(credentials.credentials, new_admin, db)

@router.put("/{uuid}", dependencies=[Depends(validate_jwt_token)])
async def update_admin(uuid: str, admin: AdminUpdate, db: Session = Depends(get_db), admin_user_payload=Depends(validate_jwt_token)):
    return adminController.update_admin(uuid, admin, db, admin_user_payload)

@router.get("/admin-list", dependencies=[Depends(validate_jwt_token)])
async def get_admin_list(
    db: Session = Depends(get_db),
    page: int = Query(1, ge=1),
    pageSize: int = Query(10, ge=1, le=100)
):
    """Get list of all admins"""
    return adminController.get_admin_list(db, page=page, pageSize=pageSize)


@router.get("/get-admin-user", dependencies=[Depends(validate_jwt_token)])
async def get_admin_user(
    db: Session = Depends(get_db),
    admin_user_payload=Depends(validate_jwt_token)
):
    """Get admin user information"""
    return adminController.get_admin_user(admin_user_payload, db)

# ========================
# Database Utilities Endpoints
# ========================

@router.post("/reset-sequences", dependencies=[Depends(validate_jwt_token)], status_code=status.HTTP_200_OK)
async def reset_database_sequences(
    request: SequenceResetRequest,
    db: Session = Depends(get_db),
    admin_user_payload=Depends(validate_jwt_token)
):
    """
    Reset all PostgreSQL sequences to fix primary key conflicts.

    This endpoint:
    - Discovers all auto-incrementing sequences in the database
    - Finds the maximum ID value for each associated table
    - Resets each sequence to start from max_id + 1

    **IMPORTANT**: This operation modifies database sequences and should only be used
    when you're experiencing primary key conflicts due to manual data insertion.

    **Parameters:**
    - confirm: Must be set to true to proceed with the reset
    - dry_run: If true, only returns status without making changes

    **Authentication:** Requires valid admin JWT token

    **Example Request:**
    ```json
    {
        "confirm": true,
        "dry_run": false
    }
    ```
    """
    return database_utils_controller.reset_sequences(request, admin_user_payload, db)


@router.get("/sequence-status", dependencies=[Depends(validate_jwt_token)], status_code=status.HTTP_200_OK)
async def get_sequence_status(
    db: Session = Depends(get_db),
    admin_user_payload=Depends(validate_jwt_token)
):
    """
    Get the current status of all database sequences.

    This is a read-only operation that shows:
    - Current sequence values
    - Maximum ID values in corresponding tables
    - Which sequences are out of sync
    - Recommended next values for each sequence

    Use this endpoint to check if sequences need to be reset before
    calling the reset-sequences endpoint.

    **Authentication:** Requires valid admin JWT token

    **Returns:** Status information for all sequences in the database
    """
    return database_utils_controller.get_sequence_status(admin_user_payload, db)


@router.delete("/{uuid}", dependencies=[Depends(validate_jwt_token)], status_code=status.HTTP_200_OK)
async def delete_admin(
    uuid: str,
    db: Session = Depends(get_db),
    admin_user_payload=Depends(validate_jwt_token)
):
    """Delete admin from both Cognito and database"""
    return adminController.delete_admin(uuid, db, admin_user_payload)

@router.get("/{uuid}", dependencies=[Depends(validate_jwt_token)])
async def get_admin_by_uuid(uuid: str, db: Session = Depends(get_db)):
    """Get admin by UUID"""
    return adminController.get_admin_by_uuid(uuid, db)

@router.post("/forgot-password", status_code=status.HTTP_200_OK)
async def initiate_forgot_password(
    request: ForgotPasswordRequest,
    db: Session = Depends(get_db)
):
    """Initiate forgot password flow - sends reset code to admin's email"""
    return adminController.initiate_forgot_password(request.username, db)

@router.post("/confirm-forgot-password", status_code=status.HTTP_200_OK)
async def confirm_forgot_password(
    request: ConfirmForgotPasswordRequest,
    db: Session = Depends(get_db)
):
    """Confirm forgot password with verification code and set new password"""
    return adminController.confirm_forgot_password(
        request.username, 
        request.confirmationCode, 
        request.newPassword, 
        db
    )

@router.post("/change-password", status_code=status.HTTP_200_OK)
async def change_admin_password(
    request: ChangePasswordRequest,
    db: Session = Depends(get_db)
):
    """Change admin password using access token"""
    return adminController.change_admin_password(request.accessToken, request.newPassword, db)

@router.delete("/delete-token-hash")
async def delete_token_hash(username: str, db: Session = Depends(get_db)):
    return adminController.delete_token_hash(username, db)