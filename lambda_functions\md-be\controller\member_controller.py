
from schemas.member import CoMemberCreate, CoMemberUpdate, BulkUpsertMembersRequest, BulkDeleteMembersRequest
from services import member_api
from sqlalchemy.orm import Session

class MemberController:
    def get_all_members(self, db: Session, page: int, pageSize: int):
        return member_api.get_all_members(db, page=page, pageSize=pageSize)
    
    def get_member_by_auth0id(self, auth0id: str, db: Session):
        return member_api.get_member_by_auth0id(auth0id, db)
    
    def get_member_by_uuid(self, uuid: str, db: Session):
        return member_api.get_member_by_uuid(uuid, db)
    
    def update_member_by_uuid(self, uuid: str, member: CoMemberUpdate, db: Session, admin_user_payload: dict):
        return member_api.update_member_by_uuid(uuid, member, db, admin_user_payload)
    
    def get_member_by_email(self, email: str, db: Session):
        return member_api.get_member_by_email(email, db)
    
    def delete_member(self, uuid: str, db: Session, admin_user_payload: dict):
        return member_api.delete_member(uuid, db, admin_user_payload)

    # Bulk operations
    def bulk_upsert_members(self, request: BulkUpsertMembersRequest, db: Session, admin_user_payload: dict):
        return member_api.bulk_upsert_members(request, db, admin_user_payload)

    def get_members_with_organizations(self, db: Session, page: int, pageSize: int):
        return member_api.get_members_with_organizations(db, page, pageSize)

    def bulk_delete_members(self, request: BulkDeleteMembersRequest, db: Session, admin_user_payload: dict):
        return member_api.bulk_delete_members(request, db, admin_user_payload)