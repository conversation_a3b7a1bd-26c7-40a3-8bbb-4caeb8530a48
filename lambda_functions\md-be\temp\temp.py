import csv

arr1 = []
arr2 = []

# Read from first CSV (e.g., Auth0 users)
with open('co_auth0_users-2025-07-28--09-37-10.csv', 'r') as file:
    reader = csv.reader(file)
    for row in reader:
        arr1.append(row[1])

# Read from second CSV (e.g., external members)
with open('co_members_external-2025-07-22--12-35-29.csv', 'r') as file:
    reader = csv.reader(file)
    for row in reader:
        arr2.append(row[6])

# Convert both arrays to sets for comparison
set1 = set(arr1)
set2 = set(arr2)

# Check if both have exactly the same data
if set1 == set2:
    print("✅ Both arrays have the same data.")
else:
    print("❌ Arrays differ.")

# Find unique items in each
only_in_arr1 = set1 - set2
only_in_arr2 = set2 - set1

print(f"🔹 Unique in arr1 (Auth0 but not in external): {len(only_in_arr1)}")
print(only_in_arr1)

print(f"🔸 Unique in arr2 (External but not in Auth0): {len(only_in_arr2)}")
print(only_in_arr2)
