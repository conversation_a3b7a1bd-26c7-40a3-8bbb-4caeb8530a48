2025-07-29 14:06:23,157 - INFO - ============================================================
2025-07-29 14:06:23,157 - INFO - Auth0 Users Data Insertion Script (Fixed)
2025-07-29 14:06:23,157 - INFO - ============================================================
2025-07-29 14:06:23,157 - INFO - CSV file: lambda_functions\md-be\temp\filtered_co_auth0_users.csv
2025-07-29 14:06:23,157 - INFO - Dry run: True
2025-07-29 14:06:23,158 - INFO - Batch size: 25
2025-07-29 14:06:23,158 - INFO - ============================================================
2025-07-29 14:06:23,158 - INFO - Starting CSV processing (dry_run=True, batch_size=25)
2025-07-29 14:06:23,158 - INFO - CSV structure validation passed. Found 17 columns.
2025-07-29 14:06:23,164 - INFO - Parsed 144 records from CSV
2025-07-29 14:06:23,165 - INFO - Validating data compatibility with database model...
2025-07-29 14:06:23,165 - INFO - Data validation passed!
2025-07-29 14:06:23,165 - INFO - DRY RUN: Would insert the following data:
2025-07-29 14:06:23,165 - INFO - Row 1: userId=apple|000228.7d300f4ae762416bb5cf32a8aeeaae9d.1241, email=<EMAIL>, emailVerified=True
2025-07-29 14:06:23,165 - INFO - Row 2: userId=apple|000296.b3f6c4513c8c44d1b7583f53d733451d.0550, email=<EMAIL>, emailVerified=True
2025-07-29 14:06:23,165 - INFO - Row 3: userId=apple|000638.7b41fda41ef942f1a80420edfdcd8bf2.2344, email=<EMAIL>, emailVerified=True
2025-07-29 14:06:23,165 - INFO - ============================================================
2025-07-29 14:06:23,165 - INFO - RESULTS
2025-07-29 14:06:23,165 - INFO - ============================================================
2025-07-29 14:06:23,166 - INFO - DRY RUN COMPLETED SUCCESSFULLY
2025-07-29 14:06:23,166 - INFO - Total records that would be processed: 144
2025-07-29 14:06:23,166 - INFO - Sample data:
2025-07-29 14:06:23,166 - INFO -   1. userId: apple|000228.7d300f4ae762416bb5cf32a8aeeaae9d.1241, email: <EMAIL>, emailVerified: True
2025-07-29 14:06:23,166 - INFO -   2. userId: apple|000296.b3f6c4513c8c44d1b7583f53d733451d.0550, email: <EMAIL>, emailVerified: True
2025-07-29 14:06:23,166 - INFO -   3. userId: apple|000638.7b41fda41ef942f1a80420edfdcd8bf2.2344, email: <EMAIL>, emailVerified: True
2025-07-29 14:06:23,166 - INFO - ============================================================
2025-07-29 14:06:32,570 - INFO - ============================================================
2025-07-29 14:06:32,570 - INFO - Auth0 Users Data Insertion Script (Fixed)
2025-07-29 14:06:32,570 - INFO - ============================================================
2025-07-29 14:06:32,570 - INFO - CSV file: lambda_functions\md-be\temp\filtered_co_auth0_users.csv
2025-07-29 14:06:32,570 - INFO - Dry run: False
2025-07-29 14:06:32,571 - INFO - Batch size: 25
2025-07-29 14:06:32,571 - INFO - ============================================================
2025-07-29 14:06:32,571 - INFO - Starting CSV processing (dry_run=False, batch_size=25)
2025-07-29 14:06:32,571 - INFO - CSV structure validation passed. Found 17 columns.
2025-07-29 14:06:32,577 - INFO - Parsed 144 records from CSV
2025-07-29 14:06:32,577 - INFO - Validating data compatibility with database model...
2025-07-29 14:06:32,577 - INFO - Data validation passed!
2025-07-29 14:06:32,577 - INFO - Secrets Manager Auth initialized for secret 'stg_member_db_rw_user' in region 'us-east-1'
2025-07-29 14:06:32,577 - INFO - Global Secrets Manager Auth initialized
2025-07-29 14:06:32,577 - INFO - Database Connection Manager initialized with Secrets Manager secret 'stg_member_db_rw_user'
2025-07-29 14:06:32,577 - INFO - Database Connection Manager created
2025-07-29 14:06:34,376 - ERROR - AWS ClientError (UnrecognizedClientException): The security token included in the request is invalid.
2025-07-29 14:06:34,382 - INFO - Full connection URL (without password): postgresql://:***@:/
2025-07-29 14:06:34,382 - ERROR - Failed to create SQLAlchemy engine: invalid literal for int() with base 10: ''
2025-07-29 14:06:34,383 - ERROR - Failed to create database session: invalid literal for int() with base 10: ''
2025-07-29 14:06:34,383 - INFO - ============================================================
2025-07-29 14:06:34,384 - INFO - RESULTS
2025-07-29 14:06:34,384 - INFO - ============================================================
2025-07-29 14:06:34,384 - ERROR - OPERATION FAILED
2025-07-29 14:06:34,384 - ERROR - Error: Database connection error: invalid literal for int() with base 10: ''
