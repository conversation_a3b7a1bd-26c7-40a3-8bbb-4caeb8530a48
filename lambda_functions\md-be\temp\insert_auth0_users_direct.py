#!/usr/bin/env python3
"""
Simplified script to insert Auth0 users data from CSV into co_auth0_users table.
This version uses direct database connection parameters instead of AWS Secrets Manager.

Usage:
    python insert_auth0_users_direct.py <csv_file> --host <host> --port <port> --database <db> --username <user> --password <pass>
    
Or set environment variables:
    DB_HOST, DB_PORT, DB_NAME, DB_USER, DB_PASSWORD
"""

import csv
import json
import logging
import sys
import os
import argparse
from datetime import datetime
from typing import List, Dict, Any, Optional
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session as SQLSession
from sqlalchemy import Column, Integer, String, Boolean, DateTime, create_engine
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.exc import IntegrityError, SQLAlchemyError
from urllib.parse import quote_plus

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('auth0_users_insert_direct.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Define the model directly
Base = declarative_base()

class CoAuth0User(Base):
    __tablename__ = "co_auth0_users"

    userId = Column("userId", String(255), primary_key=True)
    email = Column("email", String(255), nullable=False)
    emailVerified = Column("emailVerified", Boolean, nullable=False)
    name = Column("name", String(255), nullable=False)
    nickName = Column("nickName", String(255), nullable=False)
    familyName = Column("familyName", String(255), nullable=True)
    givenName = Column("givenName", String(255), nullable=True)
    picture = Column("picture", String, nullable=False)
    identities = Column("identities", JSONB, nullable=False)
    locale = Column("locale", String(255), nullable=True)
    userMetadata = Column("userMetadata", JSONB, nullable=False)
    appMetadata = Column("appMetadata", JSONB, nullable=False)
    lastIp = Column("lastIp", String(255), nullable=False)
    loginsCount = Column("loginsCount", Integer, nullable=False)
    createdAt = Column("createdAt", DateTime, nullable=True)
    updatedAt = Column("updatedAt", DateTime, nullable=True)
    lastLogin = Column("lastLogin", DateTime, nullable=True)

class DatabaseConfig:
    """Database configuration class"""
    def __init__(self, host: str, port: int, database: str, username: str, password: str):
        self.host = host
        self.port = port
        self.database = database
        self.username = username
        self.password = password
    
    def get_connection_url(self) -> str:
        """Build PostgreSQL connection URL"""
        encoded_password = quote_plus(self.password)
        return f"postgresql://{self.username}:{encoded_password}@{self.host}:{self.port}/{self.database}"

def get_database_session(config: DatabaseConfig) -> SQLSession:
    """Get a database session"""
    try:
        connection_url = config.get_connection_url()
        
        # Create engine
        engine = create_engine(
            connection_url,
            pool_size=5,
            max_overflow=10,
            pool_timeout=30,
            pool_recycle=1800,
            pool_pre_ping=True,
            connect_args={
                "sslmode": "require",
                "connect_timeout": 10
            },
            echo=False
        )
        
        # Test connection
        with engine.connect() as conn:
            conn.execute("SELECT 1")
        
        logger.info("Database connection established successfully")
        
        # Create session
        session_factory = sessionmaker(bind=engine)
        return session_factory()
        
    except Exception as e:
        logger.error(f"Failed to create database session: {str(e)}")
        raise

def parse_json_field(value: str, field_name: str) -> Any:
    """Parse JSON fields with error handling"""
    if not value or value.strip() in ['', 'NULL', 'null']:
        return [] if field_name in ['userMetadata', 'appMetadata'] else None
        
    try:
        # Handle the case where the JSON is wrapped in quotes
        if value.startswith('"') and value.endswith('"'):
            value = value[1:-1]
        
        # Replace escaped quotes
        value = value.replace('""', '"')
        
        parsed = json.loads(value)
        return parsed
    except json.JSONDecodeError as e:
        logger.warning(f"Failed to parse JSON for {field_name}: {value[:100]}... Error: {str(e)}")
        return [] if field_name in ['userMetadata', 'appMetadata'] else None

def convert_boolean_field(value: str) -> bool:
    """Convert emailVerified field from 0/1 to boolean"""
    if value in ['1', 'true', 'True', 'TRUE']:
        return True
    elif value in ['0', 'false', 'False', 'FALSE']:
        return False
    else:
        logger.warning(f"Unexpected boolean value: {value}, defaulting to False")
        return False

def parse_datetime_field(value: str) -> Optional[datetime]:
    """Parse datetime fields"""
    if not value or value.strip() in ['', 'NULL', 'null']:
        return None
        
    try:
        # Try different datetime formats
        formats = [
            '%Y-%m-%d %H:%M:%S',
            '%Y-%m-%dT%H:%M:%S',
            '%Y-%m-%d %H:%M:%S.%f',
            '%Y-%m-%dT%H:%M:%S.%f'
        ]
        
        for fmt in formats:
            try:
                return datetime.strptime(value, fmt)
            except ValueError:
                continue
                
        logger.warning(f"Could not parse datetime: {value}")
        return None
        
    except Exception as e:
        logger.warning(f"Error parsing datetime {value}: {str(e)}")
        return None

def parse_csv_row(row: Dict[str, str]) -> Optional[Dict[str, Any]]:
    """Parse a single CSV row into a format suitable for database insertion"""
    try:
        # Handle NULL values for optional fields
        def handle_null(value: str) -> Optional[str]:
            return None if value.strip() in ['', 'NULL', 'null'] else value
        
        parsed_row = {
            'userId': row['userId'].strip(),
            'email': row['email'].strip(),
            'emailVerified': convert_boolean_field(row['emailVerified']),
            'name': row['name'].strip(),
            'nickName': row['nickName'].strip(),
            'familyName': handle_null(row['familyName']),
            'givenName': handle_null(row['givenName']),
            'picture': row['picture'].strip(),
            'identities': parse_json_field(row['identities'], 'identities'),
            'locale': handle_null(row['locale']),
            'userMetadata': parse_json_field(row['userMetadata'], 'userMetadata'),
            'appMetadata': parse_json_field(row['appMetadata'], 'appMetadata'),
            'lastIp': row['lastIp'].strip(),
            'loginsCount': int(row['loginsCount']) if row['loginsCount'].strip() else 0,
            'createdAt': parse_datetime_field(row['createdAt']),
            'updatedAt': parse_datetime_field(row['updatedAt']),
            'lastLogin': parse_datetime_field(row['lastLogin'])
        }
        
        return parsed_row
        
    except Exception as e:
        logger.error(f"Error parsing row {row.get('userId', 'unknown')}: {str(e)}")
        return None

def check_existing_records(db: SQLSession, user_ids: List[str]) -> List[str]:
    """Check which user IDs already exist in the database"""
    try:
        existing_users = db.query(CoAuth0User.userId).filter(
            CoAuth0User.userId.in_(user_ids)
        ).all()
        
        existing_ids = [user.userId for user in existing_users]
        logger.info(f"Found {len(existing_ids)} existing records out of {len(user_ids)} to insert")
        return existing_ids
        
    except Exception as e:
        logger.error(f"Error checking existing records: {str(e)}")
        return []

def insert_records(db: SQLSession, records: List[Dict[str, Any]], batch_size: int = 25) -> Dict[str, int]:
    """Insert records into the database"""
    results = {"successful": 0, "failed": 0, "errors": []}
    
    for i in range(0, len(records), batch_size):
        batch = records[i:i + batch_size]
        batch_num = (i // batch_size) + 1
        logger.info(f"Processing batch {batch_num} ({len(batch)} records)")
        
        for record in batch:
            try:
                # Create CoAuth0User instance
                auth0_user = CoAuth0User(**record)
                
                # Add to session
                db.add(auth0_user)
                db.flush()  # Flush to catch any immediate errors
                
                results["successful"] += 1
                
            except IntegrityError as e:
                db.rollback()
                error_msg = f"Integrity error for user {record.get('userId', 'unknown')}: {str(e)}"
                logger.warning(error_msg)
                results["errors"].append(error_msg)
                results["failed"] += 1
                
            except Exception as e:
                db.rollback()
                error_msg = f"Error inserting user {record.get('userId', 'unknown')}: {str(e)}"
                logger.error(error_msg)
                results["errors"].append(error_msg)
                results["failed"] += 1
        
        try:
            db.commit()
            logger.info(f"Successfully committed batch {batch_num}")
        except Exception as e:
            db.rollback()
            logger.error(f"Error committing batch {batch_num}: {str(e)}")
    
    return results

def process_csv_file(csv_file: str, config: DatabaseConfig, dry_run: bool = False, batch_size: int = 25) -> Dict[str, Any]:
    """Process the CSV file and insert data"""
    logger.info(f"Processing CSV file: {csv_file} (dry_run={dry_run})")

    # Parse CSV data
    parsed_data = []
    all_user_ids = []

    try:
        with open(csv_file, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)

            for row_num, row in enumerate(reader, 1):
                parsed_row = parse_csv_row(row)
                if parsed_row:
                    parsed_data.append(parsed_row)
                    all_user_ids.append(parsed_row['userId'])
                else:
                    logger.warning(f"Skipped row {row_num} due to parsing errors")

    except Exception as e:
        logger.error(f"Error reading CSV file: {str(e)}")
        return {"success": False, "error": f"CSV reading error: {str(e)}"}

    logger.info(f"Parsed {len(parsed_data)} records from CSV")

    if dry_run:
        logger.info("DRY RUN: Would insert the following data:")
        for i, row in enumerate(parsed_data[:3]):  # Show first 3 rows
            logger.info(f"Row {i+1}: userId={row['userId']}, email={row['email']}, emailVerified={row['emailVerified']}")
        return {
            "success": True,
            "dry_run": True,
            "total_records": len(parsed_data),
            "sample_data": parsed_data[:3]
        }

    # Get database session
    try:
        db = get_database_session(config)
    except Exception as e:
        return {"success": False, "error": f"Database connection error: {str(e)}"}

    try:
        # Check existing records
        existing_ids = check_existing_records(db, all_user_ids)

        # Filter out existing records
        new_data = [row for row in parsed_data if row['userId'] not in existing_ids]
        skipped_records = len(existing_ids)

        logger.info(f"Will insert {len(new_data)} new records (skipping {skipped_records} existing)")

        if len(new_data) == 0:
            return {
                "success": True,
                "total_processed": len(parsed_data),
                "successful_inserts": 0,
                "failed_inserts": 0,
                "skipped_existing": skipped_records,
                "message": "No new records to insert"
            }

        # Insert records
        results = insert_records(db, new_data, batch_size)

        return {
            "success": True,
            "total_processed": len(parsed_data),
            "successful_inserts": results["successful"],
            "failed_inserts": results["failed"],
            "skipped_existing": skipped_records,
            "errors": results["errors"][:10]  # Return first 10 errors
        }

    except Exception as e:
        logger.error(f"Database operation error: {str(e)}")
        return {"success": False, "error": f"Database error: {str(e)}"}

    finally:
        db.close()

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Insert Auth0 users data from CSV into database')
    parser.add_argument('csv_file', help='Path to the CSV file containing Auth0 users data')
    parser.add_argument('--host', help='Database host')
    parser.add_argument('--port', type=int, default=5432, help='Database port (default: 5432)')
    parser.add_argument('--database', help='Database name')
    parser.add_argument('--username', help='Database username')
    parser.add_argument('--password', help='Database password')
    parser.add_argument('--dry-run', action='store_true', help='Perform a dry run without inserting data')
    parser.add_argument('--batch-size', type=int, default=25, help='Batch size for insertions (default: 25)')
    parser.add_argument('--verbose', '-v', action='store_true', help='Enable verbose logging')

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Check if CSV file exists
    if not os.path.exists(args.csv_file):
        logger.error(f"CSV file not found: {args.csv_file}")
        sys.exit(1)

    # Get database configuration from args or environment variables
    host = args.host or os.getenv('DB_HOST')
    port = args.port or int(os.getenv('DB_PORT', '5432'))
    database = args.database or os.getenv('DB_NAME')
    username = args.username or os.getenv('DB_USER')
    password = args.password or os.getenv('DB_PASSWORD')

    if not all([host, database, username, password]):
        logger.error("Missing database configuration. Provide via arguments or environment variables:")
        logger.error("  --host, --database, --username, --password")
        logger.error("  OR set: DB_HOST, DB_NAME, DB_USER, DB_PASSWORD")
        sys.exit(1)

    config = DatabaseConfig(host, port, database, username, password)

    logger.info("=" * 60)
    logger.info("Auth0 Users Data Insertion Script (Direct Connection)")
    logger.info("=" * 60)
    logger.info(f"CSV file: {args.csv_file}")
    logger.info(f"Database: {username}@{host}:{port}/{database}")
    logger.info(f"Dry run: {args.dry_run}")
    logger.info(f"Batch size: {args.batch_size}")
    logger.info("=" * 60)

    # Process CSV
    result = process_csv_file(args.csv_file, config, args.dry_run, args.batch_size)

    # Print results
    logger.info("=" * 60)
    logger.info("RESULTS")
    logger.info("=" * 60)

    if result["success"]:
        if args.dry_run:
            logger.info("DRY RUN COMPLETED SUCCESSFULLY")
            logger.info(f"Total records that would be processed: {result['total_records']}")
        else:
            logger.info("DATA INSERTION COMPLETED")
            logger.info(f"Total processed: {result['total_processed']}")
            logger.info(f"Successful inserts: {result['successful_inserts']}")
            logger.info(f"Failed inserts: {result['failed_inserts']}")
            logger.info(f"Skipped (existing): {result['skipped_existing']}")

            if result.get('errors'):
                logger.info("Errors encountered:")
                for error in result['errors']:
                    logger.info(f"  - {error}")
    else:
        logger.error("OPERATION FAILED")
        logger.error(f"Error: {result['error']}")
        sys.exit(1)

    logger.info("=" * 60)

if __name__ == "__main__":
    main()
