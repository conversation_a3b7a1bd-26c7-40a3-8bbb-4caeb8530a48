2025-07-29 13:49:06,384 - INFO - ============================================================
2025-07-29 13:49:06,385 - INFO - Auth0 Users Data Insertion Script
2025-07-29 13:49:06,385 - INFO - ============================================================
2025-07-29 13:49:06,385 - INFO - CSV file: lambda_functions\md-be\temp\filtered_co_auth0_users.csv
2025-07-29 13:49:06,385 - INFO - Dry run: True
2025-07-29 13:49:06,385 - INFO - Batch size: 50
2025-07-29 13:49:06,385 - INFO - ============================================================
2025-07-29 13:49:06,385 - INFO - Starting CSV processing (dry_run=True, batch_size=50)
2025-07-29 13:49:06,386 - INFO - CSV structure validation passed. Found 17 columns.
2025-07-29 13:49:06,392 - INFO - Parsed 144 records from CSV
2025-07-29 13:49:06,392 - INFO - Validating data compatibility with database model...
2025-07-29 13:49:06,392 - INFO - Data validation passed!
2025-07-29 13:49:06,392 - INFO - DRY RUN: Would insert the following data:
2025-07-29 13:49:06,392 - INFO - Row 1: userId=apple|000228.7d300f4ae762416bb5cf32a8aeeaae9d.1241, email=<EMAIL>, emailVerified=True
2025-07-29 13:49:06,393 - INFO - Row 2: userId=apple|000296.b3f6c4513c8c44d1b7583f53d733451d.0550, email=<EMAIL>, emailVerified=True
2025-07-29 13:49:06,393 - INFO - Row 3: userId=apple|000638.7b41fda41ef942f1a80420edfdcd8bf2.2344, email=<EMAIL>, emailVerified=True
2025-07-29 13:49:06,393 - INFO - ============================================================
2025-07-29 13:49:06,393 - INFO - RESULTS
2025-07-29 13:49:06,393 - INFO - ============================================================
2025-07-29 13:49:06,451 - INFO - Total records that would be processed: 144
2025-07-29 13:49:06,452 - INFO - Sample data:
2025-07-29 13:49:06,452 - INFO -   1. userId: apple|000228.7d300f4ae762416bb5cf32a8aeeaae9d.1241, email: <EMAIL>, emailVerified: True
2025-07-29 13:49:06,452 - INFO -   2. userId: apple|000296.b3f6c4513c8c44d1b7583f53d733451d.0550, email: <EMAIL>, emailVerified: True
2025-07-29 13:49:06,452 - INFO -   3. userId: apple|000638.7b41fda41ef942f1a80420edfdcd8bf2.2344, email: <EMAIL>, emailVerified: True
2025-07-29 13:49:06,452 - INFO - ============================================================
2025-07-29 13:58:30,854 - INFO - ============================================================
2025-07-29 13:58:30,855 - INFO - Auth0 Users Data Insertion Script
2025-07-29 13:58:30,855 - INFO - ============================================================
2025-07-29 13:58:30,856 - INFO - CSV file: lambda_functions\md-be\temp\filtered_co_auth0_users.csv
2025-07-29 13:58:30,856 - INFO - Dry run: False
2025-07-29 13:58:30,856 - INFO - Batch size: 25
2025-07-29 13:58:30,856 - INFO - ============================================================
2025-07-29 13:58:30,857 - INFO - Starting CSV processing (dry_run=False, batch_size=25)
2025-07-29 13:58:30,861 - INFO - CSV structure validation passed. Found 17 columns.
2025-07-29 13:58:30,876 - INFO - Parsed 144 records from CSV
2025-07-29 13:58:30,876 - INFO - Validating data compatibility with database model...
2025-07-29 13:58:30,877 - INFO - Data validation passed!
2025-07-29 13:58:30,877 - INFO - Secrets Manager Auth initialized for secret 'stg_member_db_rw_user' in region 'us-east-1'
2025-07-29 13:58:30,877 - INFO - Global Secrets Manager Auth initialized
2025-07-29 13:58:30,877 - INFO - Database Connection Manager initialized with Secrets Manager secret 'stg_member_db_rw_user'
2025-07-29 13:58:30,878 - INFO - Database Connection Manager created
2025-07-29 13:58:30,879 - DEBUG - Changing event name from creating-client-class.iot-data to creating-client-class.iot-data-plane
2025-07-29 13:58:30,881 - DEBUG - Changing event name from before-call.apigateway to before-call.api-gateway
2025-07-29 13:58:30,881 - DEBUG - Changing event name from request-created.machinelearning.Predict to request-created.machine-learning.Predict
2025-07-29 13:58:30,882 - DEBUG - Changing event name from before-parameter-build.autoscaling.CreateLaunchConfiguration to before-parameter-build.auto-scaling.CreateLaunchConfiguration
2025-07-29 13:58:30,883 - DEBUG - Changing event name from before-parameter-build.route53 to before-parameter-build.route-53
2025-07-29 13:58:30,884 - DEBUG - Changing event name from request-created.cloudsearchdomain.Search to request-created.cloudsearch-domain.Search
2025-07-29 13:58:30,884 - DEBUG - Changing event name from docs.*.autoscaling.CreateLaunchConfiguration.complete-section to docs.*.auto-scaling.CreateLaunchConfiguration.complete-section
2025-07-29 13:58:30,886 - DEBUG - Changing event name from before-parameter-build.logs.CreateExportTask to before-parameter-build.cloudwatch-logs.CreateExportTask
2025-07-29 13:58:30,887 - DEBUG - Changing event name from docs.*.logs.CreateExportTask.complete-section to docs.*.cloudwatch-logs.CreateExportTask.complete-section
2025-07-29 13:58:30,887 - DEBUG - Changing event name from before-parameter-build.cloudsearchdomain.Search to before-parameter-build.cloudsearch-domain.Search
2025-07-29 13:58:30,888 - DEBUG - Changing event name from docs.*.cloudsearchdomain.Search.complete-section to docs.*.cloudsearch-domain.Search.complete-section
2025-07-29 13:58:30,904 - DEBUG - Loading JSON file: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\botocore\data\endpoints.json
2025-07-29 13:58:30,987 - DEBUG - Loading JSON file: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\botocore\data\sdk-default-configuration.json
2025-07-29 13:58:30,989 - DEBUG - Event choose-service-name: calling handler <function handle_service_name_alias at 0x00000201C85D7B00>
2025-07-29 13:58:31,551 - DEBUG - Loading JSON file: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\botocore\data\secretsmanager\2017-10-17\service-2.json.gz
2025-07-29 13:58:31,554 - DEBUG - Loading JSON file: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\botocore\data\secretsmanager\2017-10-17\service-2.sdk-extras.json
2025-07-29 13:58:31,686 - DEBUG - Loading JSON file: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\botocore\data\secretsmanager\2017-10-17\endpoint-rule-set-1.json.gz
2025-07-29 13:58:31,688 - DEBUG - Loading JSON file: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\botocore\data\partitions.json
2025-07-29 13:58:31,688 - DEBUG - Event creating-client-class.secrets-manager: calling handler <function add_generate_presigned_url at 0x00000201C8343060>
2025-07-29 13:58:31,688 - DEBUG - Looking for endpoint for secretsmanager via: environment_service
2025-07-29 13:58:31,689 - DEBUG - Looking for endpoint for secretsmanager via: environment_global
2025-07-29 13:58:31,689 - DEBUG - Looking for endpoint for secretsmanager via: config_service
2025-07-29 13:58:31,689 - DEBUG - Looking for endpoint for secretsmanager via: config_global
2025-07-29 13:58:31,689 - DEBUG - No configured endpoint found.
2025-07-29 13:58:31,693 - DEBUG - Setting secretsmanager timeout as (60, 60)
2025-07-29 13:58:31,703 - DEBUG - Loading JSON file: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\botocore\data\_retry.json
2025-07-29 13:58:31,703 - DEBUG - Registering retry handlers for service: secretsmanager
2025-07-29 13:58:31,704 - DEBUG - Retrieving database configuration from secret 'stg_member_db_rw_user'
2025-07-29 13:58:31,704 - DEBUG - Event before-parameter-build.secrets-manager.GetSecretValue: calling handler <function generate_idempotent_uuid at 0x00000201C85F4180>
2025-07-29 13:58:31,705 - DEBUG - Event before-parameter-build.secrets-manager.GetSecretValue: calling handler <function _handle_request_validation_mode_member at 0x00000201C85F6CA0>
2025-07-29 13:58:31,705 - DEBUG - Calling endpoint provider with parameters: {'Region': 'us-east-1', 'UseDualStack': False, 'UseFIPS': False}
2025-07-29 13:58:31,705 - DEBUG - Endpoint provider result: https://secretsmanager.us-east-1.amazonaws.com
2025-07-29 13:58:31,706 - DEBUG - Event before-call.secrets-manager.GetSecretValue: calling handler <function add_recursion_detection_header at 0x00000201C85D7CE0>
2025-07-29 13:58:31,707 - DEBUG - Event before-call.secrets-manager.GetSecretValue: calling handler <function inject_api_version_header_if_needed at 0x00000201C85F5C60>
2025-07-29 13:58:31,710 - DEBUG - Making request for OperationModel(name=GetSecretValue) with params: {'url_path': '/', 'query_string': '', 'method': 'POST', 'headers': {'X-Amz-Target': 'secretsmanager.GetSecretValue', 'Content-Type': 'application/x-amz-json-1.1', 'User-Agent': 'Boto3/1.39.0 md/Botocore#1.39.0 ua/2.1 os/windows#11 md/arch#amd64 lang/python#3.13.4 md/pyimpl#CPython m/Z,D,b cfg/retry-mode#legacy Botocore/1.39.0'}, 'body': b'{"SecretId": "stg_member_db_rw_user"}', 'url': 'https://secretsmanager.us-east-1.amazonaws.com/', 'context': {'client_region': 'us-east-1', 'client_config': <botocore.config.Config object at 0x00000201C92C5160>, 'has_streaming_input': False, 'auth_type': None, 'unsigned_payload': None, 'auth_options': ['aws.auth#sigv4']}}
2025-07-29 13:58:31,718 - DEBUG - Event request-created.secrets-manager.GetSecretValue: calling handler <bound method RequestSigner.handler of <botocore.signers.RequestSigner object at 0x00000201C92C5010>>
2025-07-29 13:58:31,719 - DEBUG - Event choose-signer.secrets-manager.GetSecretValue: calling handler <function set_operation_specific_signer at 0x00000201C85D7F60>
2025-07-29 13:58:31,720 - DEBUG - Event choose-signer.secrets-manager.GetSecretValue: calling handler <function _set_auth_scheme_preference_signer at 0x00000201C85F6DE0>
2025-07-29 13:58:31,721 - DEBUG - Calculating signature using v4 auth.
2025-07-29 13:58:31,722 - DEBUG - CanonicalRequest:
POST
/

content-type:application/x-amz-json-1.1
host:secretsmanager.us-east-1.amazonaws.com
x-amz-date:20250729T082831Z
x-amz-target:secretsmanager.GetSecretValue

content-type;host;x-amz-date;x-amz-target
752c61d43198b0e1fa787713dee9228ba1b63bcd89c6d6e1cb6f098d12be049b
2025-07-29 13:58:31,722 - DEBUG - StringToSign:
AWS4-HMAC-SHA256
20250729T082831Z
20250729/us-east-1/secretsmanager/aws4_request
af427009b8d5f2fbd54023b6c3cb791f2c2de65cef124258319bb1b85648a1bb
2025-07-29 13:58:31,727 - DEBUG - Signature:
4e099dc1040118b427fca0ee481d8615fa19c363c9a186d96952dcea7692bdd8
2025-07-29 13:58:31,730 - DEBUG - Event request-created.secrets-manager.GetSecretValue: calling handler <bound method UserAgentString.rebuild_and_replace_user_agent_handler of <botocore.useragent.UserAgentString object at 0x00000201C8A26E90>>
2025-07-29 13:58:31,733 - DEBUG - Event request-created.secrets-manager.GetSecretValue: calling handler <function add_retry_headers at 0x00000201C85F6520>
2025-07-29 13:58:31,733 - DEBUG - Sending http request: <AWSPreparedRequest stream_output=False, method=POST, url=https://secretsmanager.us-east-1.amazonaws.com/, headers={'X-Amz-Target': b'secretsmanager.GetSecretValue', 'Content-Type': b'application/x-amz-json-1.1', 'User-Agent': b'Boto3/1.39.0 md/Botocore#1.39.0 ua/2.1 os/windows#11 md/arch#amd64 lang/python#3.13.4 md/pyimpl#CPython m/Z,D,b cfg/retry-mode#legacy Botocore/1.39.0', 'X-Amz-Date': b'20250729T082831Z', 'Authorization': b'AWS4-HMAC-SHA256 Credential=/20250729/us-east-1/secretsmanager/aws4_request, SignedHeaders=content-type;host;x-amz-date;x-amz-target, Signature=4e099dc1040118b427fca0ee481d8615fa19c363c9a186d96952dcea7692bdd8', 'amz-sdk-invocation-id': b'752b82cf-e4f0-4214-af69-7ba09eac94e4', 'amz-sdk-request': b'attempt=1', 'Content-Length': '37'}>
2025-07-29 13:58:31,748 - DEBUG - Certificate path: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\certifi\cacert.pem
2025-07-29 13:58:31,751 - DEBUG - Starting new HTTPS connection (1): secretsmanager.us-east-1.amazonaws.com:443
2025-07-29 13:58:33,119 - DEBUG - https://secretsmanager.us-east-1.amazonaws.com:443 "POST / HTTP/1.1" 400 107
2025-07-29 13:58:33,121 - DEBUG - Response headers: {'x-amzn-RequestId': '45cf8765-cc72-48ed-84cc-481a2bce4717', 'Content-Type': 'application/x-amz-json-1.1', 'Content-Length': '107', 'Date': 'Tue, 29 Jul 2025 08:28:22 GMT', 'Connection': 'close'}
2025-07-29 13:58:33,122 - DEBUG - Response body:
b'{"__type":"UnrecognizedClientException","message":"The security token included in the request is invalid."}'
2025-07-29 13:58:33,129 - DEBUG - Event needs-retry.secrets-manager.GetSecretValue: calling handler <botocore.retryhandler.RetryHandler object at 0x00000201C92C70E0>
2025-07-29 13:58:33,131 - DEBUG - No retry needed.
2025-07-29 13:58:33,132 - ERROR - AWS ClientError (UnrecognizedClientException): The security token included in the request is invalid.
2025-07-29 13:58:33,150 - INFO - Full connection URL (without password): postgresql://:***@:/
2025-07-29 13:58:33,151 - ERROR - Failed to create SQLAlchemy engine: invalid literal for int() with base 10: ''
