#!/usr/bin/env python3
"""
Script to insert Auth0 users data from CSV into co_auth0_users table.

This script:
1. Validates the CSV data against the database model
2. Checks for potential conflicts with existing data
3. Performs bulk insert with proper error handling
4. Converts emailVerified values (0/1) to boolean
"""

import csv
import json
import logging
import sys
import os
from datetime import datetime
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError, SQLAlchemyError
from sqlalchemy import text

# Add the parent directory to the path to import from the main application
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the existing database infrastructure
from db.db import get_db, get_session_local
from models.member import CoAuth0User

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('auth0_users_insert.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class Auth0UserInserter:
    """Class to handle Auth0 users data insertion"""
    
    def __init__(self, csv_file_path: str):
        self.csv_file_path = csv_file_path
        self.successful_inserts = 0
        self.failed_inserts = 0
        self.skipped_records = 0
        self.errors = []
        
    def validate_csv_structure(self) -> bool:
        """Validate that the CSV has the expected columns"""
        try:
            with open(self.csv_file_path, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                headers = reader.fieldnames

                if headers is None:
                    logger.error("No headers found in CSV file")
                    return False

                expected_headers = [
                    'userId', 'email', 'emailVerified', 'name', 'nickName',
                    'familyName', 'givenName', 'picture', 'identities', 'locale',
                    'userMetadata', 'appMetadata', 'lastIp', 'loginsCount',
                    'createdAt', 'updatedAt', 'lastLogin'
                ]

                missing_headers = set(expected_headers) - set(headers)
                if missing_headers:
                    logger.error(f"Missing required headers: {missing_headers}")
                    return False

                logger.info(f"CSV structure validation passed. Found {len(headers)} columns.")
                return True
                
        except Exception as e:
            logger.error(f"Error validating CSV structure: {str(e)}")
            return False
    
    def parse_json_field(self, value: str, field_name: str) -> Any:
        """Parse JSON fields with error handling"""
        if not value or value.strip() in ['', 'NULL', 'null']:
            return [] if field_name in ['userMetadata', 'appMetadata'] else None
            
        try:
            # Handle the case where the JSON is wrapped in quotes
            if value.startswith('"') and value.endswith('"'):
                value = value[1:-1]
            
            # Replace escaped quotes
            value = value.replace('""', '"')
            
            parsed = json.loads(value)
            return parsed
        except json.JSONDecodeError as e:
            logger.warning(f"Failed to parse JSON for {field_name}: {value[:100]}... Error: {str(e)}")
            return [] if field_name in ['userMetadata', 'appMetadata'] else None
    
    def convert_boolean_field(self, value: str) -> bool:
        """Convert emailVerified field from 0/1 to boolean"""
        if value in ['1', 'true', 'True', 'TRUE']:
            return True
        elif value in ['0', 'false', 'False', 'FALSE']:
            return False
        else:
            logger.warning(f"Unexpected boolean value: {value}, defaulting to False")
            return False
    
    def parse_datetime_field(self, value: str) -> Optional[datetime]:
        """Parse datetime fields"""
        if not value or value.strip() in ['', 'NULL', 'null']:
            return None
            
        try:
            # Try different datetime formats
            formats = [
                '%Y-%m-%d %H:%M:%S',
                '%Y-%m-%dT%H:%M:%S',
                '%Y-%m-%d %H:%M:%S.%f',
                '%Y-%m-%dT%H:%M:%S.%f'
            ]
            
            for fmt in formats:
                try:
                    return datetime.strptime(value, fmt)
                except ValueError:
                    continue
                    
            logger.warning(f"Could not parse datetime: {value}")
            return None
            
        except Exception as e:
            logger.warning(f"Error parsing datetime {value}: {str(e)}")
            return None
    
    def parse_csv_row(self, row: Dict[str, str]) -> Optional[Dict[str, Any]]:
        """Parse a single CSV row into a format suitable for database insertion"""
        try:
            # Handle NULL values for optional fields
            def handle_null(value: str) -> Optional[str]:
                return None if value.strip() in ['', 'NULL', 'null'] else value
            
            parsed_row = {
                'userId': row['userId'].strip(),
                'email': row['email'].strip(),
                'emailVerified': self.convert_boolean_field(row['emailVerified']),
                'name': row['name'].strip(),
                'nickName': row['nickName'].strip(),
                'familyName': handle_null(row['familyName']),
                'givenName': handle_null(row['givenName']),
                'picture': row['picture'].strip(),
                'identities': self.parse_json_field(row['identities'], 'identities'),
                'locale': handle_null(row['locale']),
                'userMetadata': self.parse_json_field(row['userMetadata'], 'userMetadata'),
                'appMetadata': self.parse_json_field(row['appMetadata'], 'appMetadata'),
                'lastIp': row['lastIp'].strip(),
                'loginsCount': int(row['loginsCount']) if row['loginsCount'].strip() else 0,
                'createdAt': self.parse_datetime_field(row['createdAt']),
                'updatedAt': self.parse_datetime_field(row['updatedAt']),
                'lastLogin': self.parse_datetime_field(row['lastLogin'])
            }
            
            return parsed_row

        except Exception as e:
            logger.error(f"Error parsing row {row.get('userId', 'unknown')}: {str(e)}")
            return None

    def check_existing_records(self, db: Session, user_ids: List[str]) -> List[str]:
        """Check which user IDs already exist in the database"""
        try:
            existing_users = db.query(CoAuth0User.userId).filter(
                CoAuth0User.userId.in_(user_ids)
            ).all()

            existing_ids = [user.userId for user in existing_users]
            logger.info(f"Found {len(existing_ids)} existing records out of {len(user_ids)} to insert")
            return existing_ids

        except Exception as e:
            logger.error(f"Error checking existing records: {str(e)}")
            return []

    def validate_data_compatibility(self, parsed_data: List[Dict[str, Any]]) -> bool:
        """Validate that the parsed data is compatible with the database model"""
        logger.info("Validating data compatibility with database model...")

        validation_errors = []

        for i, row in enumerate(parsed_data[:5]):  # Check first 5 rows as sample
            try:
                # Check required fields
                required_fields = ['userId', 'email', 'emailVerified', 'name', 'nickName', 'picture', 'lastIp']
                for field in required_fields:
                    if not row.get(field):
                        validation_errors.append(f"Row {i+1}: Missing required field '{field}'")

                # Check data types
                if not isinstance(row.get('emailVerified'), bool):
                    validation_errors.append(f"Row {i+1}: emailVerified must be boolean")

                if not isinstance(row.get('loginsCount'), int):
                    validation_errors.append(f"Row {i+1}: loginsCount must be integer")

                # Check JSON fields
                json_fields = ['identities', 'userMetadata', 'appMetadata']
                for field in json_fields:
                    value = row.get(field)
                    if value is not None and not isinstance(value, (list, dict)):
                        validation_errors.append(f"Row {i+1}: {field} must be valid JSON")

            except Exception as e:
                validation_errors.append(f"Row {i+1}: Validation error - {str(e)}")

        if validation_errors:
            logger.error("Data validation failed:")
            for error in validation_errors:
                logger.error(f"  - {error}")
            return False

        logger.info("Data validation passed!")
        return True

    def insert_batch(self, db: Session, batch_data: List[Dict[str, Any]]) -> int:
        """Insert a batch of records"""
        successful_count = 0

        for row_data in batch_data:
            try:
                # Create CoAuth0User instance
                auth0_user = CoAuth0User(**row_data)

                # Add to session
                db.add(auth0_user)
                db.flush()  # Flush to catch any immediate errors

                successful_count += 1

            except IntegrityError as e:
                db.rollback()
                error_msg = f"Integrity error for user {row_data.get('userId', 'unknown')}: {str(e)}"
                logger.warning(error_msg)
                self.errors.append(error_msg)
                self.failed_inserts += 1

            except Exception as e:
                db.rollback()
                error_msg = f"Error inserting user {row_data.get('userId', 'unknown')}: {str(e)}"
                logger.error(error_msg)
                self.errors.append(error_msg)
                self.failed_inserts += 1

        try:
            db.commit()
            logger.info(f"Successfully committed batch of {successful_count} records")
            return successful_count
        except Exception as e:
            db.rollback()
            logger.error(f"Error committing batch: {str(e)}")
            return 0

    def process_csv(self, batch_size: int = 50, dry_run: bool = False) -> Dict[str, Any]:
        """Process the CSV file and insert data into the database"""
        logger.info(f"Starting CSV processing (dry_run={dry_run}, batch_size={batch_size})")

        # Validate CSV structure
        if not self.validate_csv_structure():
            return {"success": False, "error": "CSV structure validation failed"}

        # Parse all data first
        parsed_data = []
        all_user_ids = []

        try:
            with open(self.csv_file_path, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)

                for row_num, row in enumerate(reader, 1):
                    parsed_row = self.parse_csv_row(row)
                    if parsed_row:
                        parsed_data.append(parsed_row)
                        all_user_ids.append(parsed_row['userId'])
                    else:
                        self.failed_inserts += 1
                        logger.warning(f"Skipped row {row_num} due to parsing errors")

        except Exception as e:
            logger.error(f"Error reading CSV file: {str(e)}")
            return {"success": False, "error": f"CSV reading error: {str(e)}"}

        logger.info(f"Parsed {len(parsed_data)} records from CSV")

        # Validate data compatibility
        if not self.validate_data_compatibility(parsed_data):
            return {"success": False, "error": "Data validation failed"}

        if dry_run:
            logger.info("DRY RUN: Would insert the following data:")
            for i, row in enumerate(parsed_data[:3]):  # Show first 3 rows
                logger.info(f"Row {i+1}: userId={row['userId']}, email={row['email']}, emailVerified={row['emailVerified']}")
            return {
                "success": True,
                "dry_run": True,
                "total_records": len(parsed_data),
                "sample_data": parsed_data[:3]
            }

        # Get database session using the existing infrastructure
        session_factory = get_session_local()
        db = session_factory()

        try:
            existing_ids = self.check_existing_records(db, all_user_ids)

            # Filter out existing records
            new_data = [row for row in parsed_data if row['userId'] not in existing_ids]
            self.skipped_records = len(existing_ids)

            logger.info(f"Will insert {len(new_data)} new records (skipping {self.skipped_records} existing)")

            # Process in batches
            total_inserted = 0
            for i in range(0, len(new_data), batch_size):
                batch = new_data[i:i + batch_size]
                batch_num = (i // batch_size) + 1
                logger.info(f"Processing batch {batch_num} ({len(batch)} records)")

                inserted_count = self.insert_batch(db, batch)
                total_inserted += inserted_count
                self.successful_inserts += inserted_count

            return {
                "success": True,
                "total_processed": len(parsed_data),
                "successful_inserts": self.successful_inserts,
                "failed_inserts": self.failed_inserts,
                "skipped_existing": self.skipped_records,
                "errors": self.errors[:10]  # Return first 10 errors
            }

        except Exception as e:
            logger.error(f"Database operation error: {str(e)}")
            return {"success": False, "error": f"Database error: {str(e)}"}

        finally:
            db.close()


def main():
    """Main function to run the script"""
    import argparse

    parser = argparse.ArgumentParser(description='Insert Auth0 users data from CSV into database')
    parser.add_argument('csv_file', help='Path to the CSV file containing Auth0 users data')
    parser.add_argument('--dry-run', action='store_true', help='Perform a dry run without inserting data')
    parser.add_argument('--batch-size', type=int, default=50, help='Batch size for insertions (default: 50)')
    parser.add_argument('--verbose', '-v', action='store_true', help='Enable verbose logging')

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Check if CSV file exists
    if not os.path.exists(args.csv_file):
        logger.error(f"CSV file not found: {args.csv_file}")
        sys.exit(1)

    logger.info("=" * 60)
    logger.info("Auth0 Users Data Insertion Script")
    logger.info("=" * 60)
    logger.info(f"CSV file: {args.csv_file}")
    logger.info(f"Dry run: {args.dry_run}")
    logger.info(f"Batch size: {args.batch_size}")
    logger.info("=" * 60)

    # Create inserter and process
    inserter = Auth0UserInserter(args.csv_file)
    result = inserter.process_csv(batch_size=args.batch_size, dry_run=args.dry_run)

    # Print results
    logger.info("=" * 60)
    logger.info("RESULTS")
    logger.info("=" * 60)

    if result["success"]:
        if args.dry_run:
            logger.info("✅ DRY RUN COMPLETED SUCCESSFULLY")
            logger.info(f"Total records that would be processed: {result['total_records']}")
            logger.info("Sample data:")
            for i, row in enumerate(result.get('sample_data', []), 1):
                logger.info(f"  {i}. userId: {row['userId']}, email: {row['email']}, emailVerified: {row['emailVerified']}")
        else:
            logger.info("✅ DATA INSERTION COMPLETED")
            logger.info(f"Total processed: {result['total_processed']}")
            logger.info(f"Successful inserts: {result['successful_inserts']}")
            logger.info(f"Failed inserts: {result['failed_inserts']}")
            logger.info(f"Skipped (existing): {result['skipped_existing']}")

            if result['errors']:
                logger.info("Errors encountered:")
                for error in result['errors']:
                    logger.info(f"  - {error}")
    else:
        logger.error("❌ OPERATION FAILED")
        logger.error(f"Error: {result['error']}")
        sys.exit(1)

    logger.info("=" * 60)


if __name__ == "__main__":
    main()
