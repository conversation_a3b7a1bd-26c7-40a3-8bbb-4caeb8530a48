#!/usr/bin/env python3
"""
Test script to validate the CSV data before insertion.
This script performs comprehensive validation without touching the database.
"""

import csv
import json
import sys
import os
from datetime import datetime
from typing import Dict, List, Any

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def validate_csv_data(csv_file_path: str) -> Dict[str, Any]:
    """Validate CSV data comprehensively"""
    
    results = {
        "valid": True,
        "total_rows": 0,
        "valid_rows": 0,
        "invalid_rows": 0,
        "errors": [],
        "warnings": [],
        "sample_data": [],
        "statistics": {}
    }
    
    try:
        with open(csv_file_path, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            
            # Check headers
            expected_headers = [
                'userId', 'email', 'emailVerified', 'name', 'nickName',
                'familyName', 'givenName', 'picture', 'identities', 'locale',
                'userMetadata', 'appMetadata', 'lastIp', 'loginsCount',
                'createdAt', 'updatedAt', 'lastLogin'
            ]
            
            missing_headers = set(expected_headers) - set(reader.fieldnames)
            if missing_headers:
                results["valid"] = False
                results["errors"].append(f"Missing headers: {missing_headers}")
                return results
            
            # Statistics tracking
            email_verified_stats = {"true": 0, "false": 0}
            provider_stats = {}
            
            for row_num, row in enumerate(reader, 1):
                results["total_rows"] += 1
                row_valid = True
                row_errors = []
                
                # Validate required fields
                required_fields = ['userId', 'email', 'name', 'nickName', 'picture', 'lastIp']
                for field in required_fields:
                    if not row[field] or row[field].strip() in ['', 'NULL']:
                        row_errors.append(f"Missing required field: {field}")
                        row_valid = False
                
                # Validate emailVerified
                email_verified = row['emailVerified'].strip()
                if email_verified in ['1', 'true', 'True']:
                    email_verified_stats["true"] += 1
                elif email_verified in ['0', 'false', 'False']:
                    email_verified_stats["false"] += 1
                else:
                    row_errors.append(f"Invalid emailVerified value: {email_verified}")
                    row_valid = False
                
                # Validate loginsCount
                try:
                    logins_count = int(row['loginsCount']) if row['loginsCount'].strip() else 0
                except ValueError:
                    row_errors.append(f"Invalid loginsCount: {row['loginsCount']}")
                    row_valid = False
                
                # Validate JSON fields
                json_fields = ['identities', 'userMetadata', 'appMetadata']
                for field in json_fields:
                    value = row[field].strip()
                    if value and value not in ['NULL', 'null', '[]', '{}']:
                        try:
                            # Handle quoted JSON
                            if value.startswith('"') and value.endswith('"'):
                                value = value[1:-1].replace('""', '"')
                            json.loads(value)
                        except json.JSONDecodeError:
                            row_errors.append(f"Invalid JSON in {field}")
                            row_valid = False
                
                # Extract provider statistics from identities
                try:
                    identities_str = row['identities'].strip()
                    if identities_str and identities_str not in ['NULL', 'null']:
                        if identities_str.startswith('"') and identities_str.endswith('"'):
                            identities_str = identities_str[1:-1].replace('""', '"')
                        identities = json.loads(identities_str)
                        if isinstance(identities, list) and identities:
                            provider = identities[0].get('provider', 'unknown')
                            provider_stats[provider] = provider_stats.get(provider, 0) + 1
                except:
                    pass
                
                # Validate datetime fields
                datetime_fields = ['createdAt', 'updatedAt', 'lastLogin']
                for field in datetime_fields:
                    value = row[field].strip()
                    if value and value not in ['NULL', 'null']:
                        try:
                            datetime.strptime(value, '%Y-%m-%d %H:%M:%S')
                        except ValueError:
                            row_errors.append(f"Invalid datetime format in {field}: {value}")
                            row_valid = False
                
                if row_valid:
                    results["valid_rows"] += 1
                    # Store first 3 valid rows as samples
                    if len(results["sample_data"]) < 3:
                        results["sample_data"].append({
                            "userId": row['userId'],
                            "email": row['email'],
                            "emailVerified": email_verified in ['1', 'true', 'True'],
                            "name": row['name'],
                            "provider": provider_stats
                        })
                else:
                    results["invalid_rows"] += 1
                    results["errors"].append(f"Row {row_num}: {', '.join(row_errors)}")
                    if len(results["errors"]) > 10:  # Limit error reporting
                        results["errors"].append("... (more errors truncated)")
                        break
            
            # Compile statistics
            results["statistics"] = {
                "email_verified_distribution": email_verified_stats,
                "provider_distribution": provider_stats,
                "total_providers": len(provider_stats)
            }
            
            # Final validation
            if results["invalid_rows"] > 0:
                results["valid"] = False
            
    except Exception as e:
        results["valid"] = False
        results["errors"].append(f"File processing error: {str(e)}")
    
    return results


def main():
    """Main function"""
    if len(sys.argv) != 2:
        print("Usage: python test_data_validation.py <csv_file_path>")
        sys.exit(1)
    
    csv_file = sys.argv[1]
    
    if not os.path.exists(csv_file):
        print(f"Error: File not found: {csv_file}")
        sys.exit(1)
    
    print("=" * 60)
    print("CSV DATA VALIDATION REPORT")
    print("=" * 60)
    print(f"File: {csv_file}")
    print()
    
    results = validate_csv_data(csv_file)
    
    print(f"Total rows: {results['total_rows']}")
    print(f"Valid rows: {results['valid_rows']}")
    print(f"Invalid rows: {results['invalid_rows']}")
    print()
    
    if results["statistics"]:
        print("STATISTICS:")
        print(f"Email verified distribution: {results['statistics']['email_verified_distribution']}")
        print(f"Provider distribution: {results['statistics']['provider_distribution']}")
        print()
    
    if results["sample_data"]:
        print("SAMPLE VALID DATA:")
        for i, sample in enumerate(results["sample_data"], 1):
            print(f"  {i}. userId: {sample['userId']}")
            print(f"     email: {sample['email']}")
            print(f"     emailVerified: {sample['emailVerified']}")
            print(f"     name: {sample['name']}")
            print()
    
    if results["errors"]:
        print("ERRORS FOUND:")
        for error in results["errors"]:
            print(f"  - {error}")
        print()
    
    if results["warnings"]:
        print("WARNINGS:")
        for warning in results["warnings"]:
            print(f"  - {warning}")
        print()
    
    if results["valid"]:
        print("✅ VALIDATION PASSED - Data is ready for insertion")
        sys.exit(0)
    else:
        print("❌ VALIDATION FAILED - Please fix errors before insertion")
        sys.exit(1)


if __name__ == "__main__":
    main()
